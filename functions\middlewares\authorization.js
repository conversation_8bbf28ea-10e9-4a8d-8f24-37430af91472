const logger = require('firebase-functions/logger');

// middleware for doing permission-based authorization
/**
 * @param  {string[]} permittedRoles
 */
function permit(...permittedRoles) {
  // return a middleware
  return (request, response, next) => {
    /** @type {{ name: string, value: string, permissions: string }} apiKey */
    const apiKey = request.apiKey;

    /** @type {string[]} */
    const permissions = apiKey.permissions.split(';');

    if (apiKey && permittedRoles.some((perm) => permissions.includes(perm))) {
      logger.info('Permission granted', {
        keyName: apiKey.name,
        claim: permittedRoles,
      });
      next();
    } else {
      logger.warn('Permission denied', {
        keyName: apiKey.name,
        claim: permittedRoles,
      });

      response.status(403).json({
        message: 'Forbidden',
        detail: 'You do not have the necessary permissions to access this resource.',
      });
    }
  };
}

module.exports = permit;
