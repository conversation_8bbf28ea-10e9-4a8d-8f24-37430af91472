{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"serve": "firebase emulators:start", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "index.js", "dependencies": {"@notionhq/client": "^4.0.1", "cors": "^2.8.5", "express": "^4.21.2", "firebase-admin": "^11.11.1", "firebase-functions": "^4.9.0", "helmet": "^7.2.0", "joi": "^17.13.3"}, "devDependencies": {"@eslint/js": "^9.13.0", "eslint": "^9.31.0", "firebase-functions-test": "^3.4.1", "globals": "^15.15.0"}, "private": true, "version": "1.0.0", "keywords": [], "author": "", "license": "ISC"}