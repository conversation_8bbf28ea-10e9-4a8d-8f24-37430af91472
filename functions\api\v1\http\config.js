const logger = require('firebase-functions/logger');
const express = require('express');
const Joi = require('joi');
const { Timestamp } = require('firebase-admin/firestore');

const admin = require('../../../admin.js');
const permit = require('../../../middlewares/authorization.js');

const bucket = admin.storage().bucket();
const db = admin.firestore();
const router = express.Router();

const configSchema = Joi.object({
  name: Joi.string().allow('', null),
  skus: Joi.array().items(Joi.string()).required(),
  quotationSkus: Joi.array().items(Joi.string()),
  sessionId: Joi.string().guid().required(),
  country: Joi.string(),
  isQuotation: Joi.boolean(),
});

const EXPIRES_MS = 300_000;

router.post('/configs', permit('create/configuration'), async (req, res) => {
  const { error, value } = configSchema.validate(req.body);

  if (error) {
    logger.warn('Invalid configuration', { error });
    return res.status(400).json({
      message: 'Bad Request',
      detail: error.message,
    });
  }

  const docRef = await db.collection('configs').add({
    ...value,
    created_at: Timestamp.now(),
    uploaded: false,
  });

  const { id } = docRef;
  const fileName = `${id}.glb`;
  const storageOptions = {
    version: 'v4',
    action: 'write',
    expires: Date.now() + EXPIRES_MS,
    contentType: 'model/gltf-binary',
  };
  const [url] = await bucket.file(fileName).getSignedUrl(storageOptions);

  logger.info('Create configuration', { id, ...value });
  return res.status(201).json({
    id,
    ...value,
    uploadUrl: url,
  });
});

router.get('/configs/:id', permit("read/configuration"), async (req, res) => {
  const configRef = db.collection('configs').doc(req.params.id);
  const snapshot = await configRef.get();

  if (!snapshot.exists) {
    logger.warn('Configuration not found', { id: req.params.id });
    return res.status(404).json({
      message: 'Not Found',
      detail: 'Configuration not found',
    });
  }

  const config = snapshot.data();

  delete config.created_at;
  delete config.sessionId;
  config.id = snapshot.id;

  const { id } = snapshot;
  const fileName = `${id}.glb`;
  const storageOptions = {
    version: 'v4',
    action: 'read',
    expires: Date.now() + EXPIRES_MS,
  };
  const [url] = await bucket.file(fileName).getSignedUrl(storageOptions);

  return res.json({ ...config, url });
});

router.post('/configs/:id', permit('create/configuration'), async (req, res) => {
  const { id } = req.params;
  const { error, value } = configSchema.validate(req.body);

  if (error) {
    logger.warn('Invalid configuration', { error });
    return res.status(400).json({
      message: 'Bad Request',
      detail: error.message,
    });
  }

  const configRef = db.collection('configs').doc(id);
  const snapshot = await configRef.get();

  if (!snapshot.exists) {
    logger.warn('Configuration not found', { id });
    return res.status(404).json({
      message: 'Not Found',
      detail: 'Configuration not found',
    });
  }

  await configRef.update({ ...value, uploaded: false }).catch((e) => {
    logger.error('Error updating configuration', { id, error: e });
    return res.status(500).json({
      message: 'Internal Server Error',
      detail: 'Error updating configuration',
    });
  });

  const fileName = `${id}.glb`;
  const storageOptions = {
    version: 'v4',
    action: 'write',
    expires: Date.now() + EXPIRES_MS,
    contentType: 'model/gltf-binary',
  };
  const [url] = await bucket.file(fileName).getSignedUrl(storageOptions);

  logger.info('Update configuration', { id, ...value });
  return res.json({
    id,
    ...value,
    uploadUrl: url,
  });
});

module.exports = router;
