const logger = require("firebase-functions/logger");
const express = require("express");
const { Client } = require("@notionhq/client");
const permit = require("../../../middlewares/authorization");

const router = express.Router();

router.get("/products/:id/exists", permit("*"), async (req, res) => {
  try {
    // Access the secrets from environment variables
    const notionToken = process.env.NOTION_TOKEN;
    const notionDatabaseId = process.env.NOTION_DATABASE_ID;

    // Validate that both secrets are available
    if (!notionToken) {
      logger.error("NOTION_TOKEN secret is not available");
      return res.status(500).json({
        message: "Internal Server Error",
        detail: "NOTION_TOKEN configuration error",
      });
    }

    if (!notionDatabaseId) {
      logger.error("NOTION_DATABASE_ID secret is not available");
      return res.status(500).json({
        message: "Internal Server Error",
        detail: "NOTION_DATABASE_ID configuration error",
      });
    }

    // Initialize Notion client
    const notion = new Client({ auth: notionToken });

    // Log that we have access to the secrets (without logging the actual values)
    logger.info("Notion secrets accessed successfully", {
      id: req.params.id,
      hasToken: !!notionToken,
      hasDatabaseId: !!notionDatabaseId,
    });

    // Create database query with filters
    const databaseQuery = {
      database_id: notionDatabaseId,
      filter: {
        and: [
          {
            property: "usage",
            select: {
              equals: "Configurateur"
            }
          },
          {
            property: "sku",
            rich_text: {
              equals: req.params.id
            }
          }
        ]
      }
    };

    logger.info("Executing Notion database query", {
      databaseId: notionDatabaseId,
      filters: {
        usage: "Configurateur",
        sku: req.params.id
      }
    });

    // Execute the database query
    const response = await notion.databases.query(databaseQuery);

    // Log the complete response
    logger.info("Notion database query response", {
      id: req.params.id,
      resultsCount: response.results.length,
      hasNextCursor: !!response.next_cursor,
      completeResponse: response
    });

    // Return the response
    res.status(200).json({
      id: req.params.id,
      exists: response.results.length > 0,
      resultsCount: response.results.length,
      results: response.results,
      hasMore: response.has_more,
      nextCursor: response.next_cursor
    });

  } catch (error) {
    logger.error("Error in products exists endpoint", {
      error: error.message,
      stack: error.stack,
      id: req.params.id,
    });

    res.status(500).json({
      message: "Internal Server Error",
      detail: "An error occurred while querying the Notion database",
    });
  }
});

module.exports = router;
