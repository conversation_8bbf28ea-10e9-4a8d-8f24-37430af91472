const logger = require("firebase-functions/logger");
const express = require("express");
const { Client } = require("@notionhq/client");
const permit = require("../../../middlewares/authorization");

const router = express.Router();

router.get("/products/:id/exists", async (req, res) => {
  try {
    // eslint-disable-next-line no-undef
    const notionToken = process.env.NOTION_TOKEN;
    // eslint-disable-next-line no-undef
    const notionDatabaseId = process.env.NOTION_DATABASE_ID;

    const notion = new Client({ auth: notionToken });

    if (!notionToken) {
      logger.error("NOTION_TOKEN secret is not available");
      return res.status(500).json({
        message: "Internal Server Error",
        detail: "Configuration error",
      });
    }

    // Log that we have access to the token (without logging the actual value)
    logger.info("NOTION_TOKEN secret accessed successfully", {
      id: req.params.id,
      hasToken: !!notionToken,
      hasDatabaseId: !!notionDatabaseId,
    });

    // TODO: Implement your Notion API logic here
    // For now, just return a success response
    res.status(200).json({
      id: req.params.id,
      exists: true,
      message: "NOTION_TOKEN is available",
    });
  } catch (error) {
    logger.error("Error in exists endpoint", {
      error: error.message,
      id: req.params.id,
    });
    res.status(500).json({
      message: "Internal Server Error",
      detail: "An error occurred while processing the request",
    });
  }
});

module.exports = router;
