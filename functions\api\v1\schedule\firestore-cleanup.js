const { onSchedule } = require('firebase-functions/v2/scheduler');
const logger = require('firebase-functions/logger');
const admin = require('../../../admin');
const firestore = admin.firestore();

async function deleteTestEntries(event) {
  try {
    const configsRef = firestore.collection('configs');
    const querySnapshot = await configsRef.where('value', '==', 'test').get();

    const deletePromises = [];
    querySnapshot.forEach((doc) => {
      deletePromises.push(doc.ref.delete());
    });

    await Promise.all(deletePromises);

    logger.log('Scheduled deletion complete.');
  } catch (error) {
    logger.error('Error deleting documents:', error);
  }
}

exports.firestoreCleanup = onSchedule('every day 00:00', deleteTestEntries);
