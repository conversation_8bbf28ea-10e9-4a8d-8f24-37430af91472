const logger = require('firebase-functions/logger');
const express = require('express');
const router = express.Router();

const permit = require('../../../middlewares/authorization');
const adminApp = require('../../../admin');
const db = adminApp.firestore();
const webAppHost = "https://options-ar.web.app";
const { Timestamp } = require('firebase-admin/firestore');

router.get('/health', (req, res) => {
  logger.info('Health check', { structuredData: true });
  res.status(200).send();
});

router.get('/config-id', permit('create/configuration-id'), async (req, res) => {
  try {
    const docRef = await db.collection('configs').add({
      name: "",
      created_at: Timestamp.now(),
      uploaded: false,
    });

    const { id } = docRef;

    logger.info('Config created', { configId: id });

    res.status(201).send({
      id: id,
      url: `${webAppHost}/?id=${id}`,
    });
  } catch (error) {
    logger.error('Error creating config', error);
    res.status(500).send({
      message: 'Internal Server Error',
      detail: 'An error occurred while creating the configuration',
    });
  }
});

module.exports = router;
