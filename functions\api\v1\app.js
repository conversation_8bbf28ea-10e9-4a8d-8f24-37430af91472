const { onRequest } = require('firebase-functions/v2/https');
const express = require('express');
const helmet = require('helmet').default;
const cors = require('cors');
const apiKeyMiddleware = require('../../middlewares/authentication');

const configIdRouter = require('./http/config-id');
const configRouter = require('./http/config');

const API_PREFIX = 'api';

const app = express();

app.use(cors());
app.use(helmet());
app.use(apiKeyMiddleware);

// Middleware for removing X-Powered-By header on each request response.
// This is a workaround for complying with security best practices.
// Remove or disable header won't work on Firebase Functions.
app.use((_, res, next) => {
  res.setHeader('X-Powered-By', '');
  next();
});

// Rewrite middleware to use a custom domain with Firebase Hosting rewrites.
app.use((req, _, next) => {
  if (req.url.indexOf(`/${API_PREFIX}/`) === 0) {
    req.url = req.url.substring(API_PREFIX.length + 1);
  }
  next();
});

app.use('/v1', configIdRouter);
app.use('/v1', configRouter);

exports.api = onRequest(app);
