const { onObjectFinalized } = require('firebase-functions/v2/storage');
const logger = require('firebase-functions/logger');
const path = require('path');
const admin = require('../../../admin');
const db = admin.firestore();

async function validateModel(event) {
  const { name, contentType } = event.data;

  if (contentType !== 'model/gltf-binary') {
    logger.info(`Ignoring object '${name}' with content type`, contentType);
    return;
  }

  const fileName = path.parse(name);
  const configRef = db.collection('configs').doc(fileName.name);

  await configRef
    .update({
      uploaded: true,
    })
    .then(() => {
      console.log(`Config '${name}' updated`);
    })
    .catch((error) => {
      console.warn(`Cant update config '${name}' - it does not exist`, error);
    });
}

exports.validateModel = onObjectFinalized({ bucket: 'options-configurator' }, validateModel);
