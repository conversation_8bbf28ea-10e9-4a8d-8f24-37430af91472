const logger = require('firebase-functions/logger');
const admin = require('../admin.js');
const rtdb = admin.database();

module.exports = async (req, res, next) => {
  const apiKey = req.headers['x-api-key'];

  if (!apiKey) {
    logger.warn('API key is missing');

    res.status(401).send({
      message: 'Unauthorized',
      detail: 'Make sure to include the X-API-KEY header in your request.',
    });

    return;
  }

  const keysRef = rtdb.ref('api_keys');

  try {
    const snapshot = await keysRef.once('value');

    /** @type Array.<{ name: string, value: string }> keys */
    const keys = snapshot.val();

    if (!keys) {
      logger.error('No API keys in database');
      throw new Error('No API keys in database');
    }

    const key = keys.find((x) => x.value === apiKey);
    req.apiKey = key;

    if (!key) {
      logger.warn('API key is invalid', {
        api<PERSON><PERSON>,
      });

      res.status(401).send({
        message: 'Unauthorized',
        detail: 'Invalid API key',
      });

      return;
    }
  } catch (error) {
    logger.error('Error fetching API keys', {
      error,
    });

    res.status(500).send({ message: 'Internal Server Error' });

    return;
  }

  next();
};
